/// خدمة إدارة قوالب الرواتب
/// توفر وظائف إنشاء وإدارة وتطبيق قوالب الرواتب للموظفين
library;

import '../database/database_helper.dart';
import '../models/hr_models.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../services/validation_service.dart';
import '../exceptions/validation_exception.dart';

class SalaryTemplateService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// الحصول على جميع قوالب الرواتب
  Future<List<SalaryTemplate>> getAllTemplates({
    bool activeOnly = false,
    String? searchQuery,
  }) async {
    try {
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (activeOnly) {
        whereClause += ' AND is_active = ?';
        whereArgs.add(1);
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        whereClause += ' AND (name LIKE ? OR description LIKE ?)';
        final searchPattern = '%$searchQuery%';
        whereArgs.addAll([searchPattern, searchPattern]);
      }

      final result = await db.query(
        AppConstants.salaryTemplatesTable,
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'is_default DESC, name ASC',
      );

      return result.map((map) => SalaryTemplate.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب قوالب الرواتب',
        category: 'SalaryTemplateService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على قالب راتب بالمعرف
  Future<SalaryTemplate?> getTemplateById(int id) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.salaryTemplatesTable,
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return SalaryTemplate.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب قالب الراتب',
        category: 'SalaryTemplateService',
        data: {'id': id, 'error': e.toString()},
      );
      return null;
    }
  }

  /// الحصول على القالب الافتراضي
  Future<SalaryTemplate?> getDefaultTemplate() async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.salaryTemplatesTable,
        where: 'is_default = ? AND is_active = ?',
        whereArgs: [1, 1],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return SalaryTemplate.fromMap(result.first);
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب القالب الافتراضي',
        category: 'SalaryTemplateService',
        data: {'error': e.toString()},
      );
      return null;
    }
  }

  /// إنشاء قالب راتب جديد
  Future<SalaryTemplate> createTemplate(SalaryTemplate template) async {
    try {
      // التحقق من صحة البيانات
      await _validateTemplate(template);

      final db = await _databaseHelper.database;

      // التحقق من عدم تكرار الاسم
      final existingTemplate = await _getTemplateByName(template.name);
      if (existingTemplate != null) {
        throw ValidationException('اسم القالب موجود مسبقاً');
      }

      // إذا كان القالب افتراضي، إلغاء الافتراضية من القوالب الأخرى
      if (template.isDefault) {
        await _clearDefaultTemplates();
      }

      final now = DateTime.now();
      final templateData = template.copyWith(createdAt: now, updatedAt: now);

      final id = await db.insert(
        AppConstants.salaryTemplatesTable,
        templateData.toMap(),
      );

      final newTemplate = templateData.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'SalaryTemplate',
        entityId: id,
        description: 'إنشاء قالب راتب جديد: ${template.name}',
        newValues: newTemplate.toMap(),
      );

      LoggingService.info(
        'تم إنشاء قالب راتب جديد بنجاح',
        category: 'SalaryTemplateService',
        data: {'id': id, 'name': template.name},
      );

      return newTemplate;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء قالب الراتب',
        category: 'SalaryTemplateService',
        data: {'template': template.toMap(), 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تحديث قالب راتب
  Future<SalaryTemplate> updateTemplate(SalaryTemplate template) async {
    try {
      if (template.id == null) {
        throw ValidationException('معرف القالب مطلوب للتحديث');
      }

      // التحقق من وجود القالب
      final existingTemplate = await getTemplateById(template.id!);
      if (existingTemplate == null) {
        throw ValidationException('القالب غير موجود');
      }

      // التحقق من صحة البيانات
      await _validateTemplate(template);

      final db = await _databaseHelper.database;

      // التحقق من عدم تكرار الاسم (باستثناء القالب الحالي)
      final duplicateTemplate = await _getTemplateByName(template.name);
      if (duplicateTemplate != null && duplicateTemplate.id != template.id) {
        throw ValidationException('اسم القالب موجود مسبقاً');
      }

      // إذا كان القالب افتراضي، إلغاء الافتراضية من القوالب الأخرى
      if (template.isDefault && !existingTemplate.isDefault) {
        await _clearDefaultTemplates();
      }

      final updatedTemplate = template.copyWith(updatedAt: DateTime.now());

      await db.update(
        AppConstants.salaryTemplatesTable,
        updatedTemplate.toMap(),
        where: 'id = ?',
        whereArgs: [template.id],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'SalaryTemplate',
        entityId: template.id!,
        description: 'تحديث قالب راتب: ${template.name}',
        oldValues: existingTemplate.toMap(),
        newValues: updatedTemplate.toMap(),
      );

      LoggingService.info(
        'تم تحديث قالب الراتب بنجاح',
        category: 'SalaryTemplateService',
        data: {'id': template.id, 'name': template.name},
      );

      return updatedTemplate;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث قالب الراتب',
        category: 'SalaryTemplateService',
        data: {'template': template.toMap(), 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// حذف قالب راتب
  Future<void> deleteTemplate(int id) async {
    try {
      final template = await getTemplateById(id);
      if (template == null) {
        throw ValidationException('القالب غير موجود');
      }

      // التحقق من عدم استخدام القالب
      final isUsed = await _isTemplateUsed(id);
      if (isUsed) {
        throw ValidationException(
          'لا يمكن حذف القالب لأنه مستخدم من قبل موظفين',
        );
      }

      final db = await _databaseHelper.database;
      await db.delete(
        AppConstants.salaryTemplatesTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'DELETE',
        entityType: 'SalaryTemplate',
        entityId: id,
        description: 'حذف قالب راتب: ${template.name}',
        oldValues: template.toMap(),
      );

      LoggingService.info(
        'تم حذف قالب الراتب بنجاح',
        category: 'SalaryTemplateService',
        data: {'id': id, 'name': template.name},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف قالب الراتب',
        category: 'SalaryTemplateService',
        data: {'id': id, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تطبيق قالب على موظف
  Future<void> applyTemplateToEmployee({
    required int templateId,
    required int employeeId,
    DateTime? effectiveDate,
  }) async {
    try {
      final template = await getTemplateById(templateId);
      if (template == null) {
        throw ValidationException('القالب غير موجود');
      }

      if (!template.isActive) {
        throw ValidationException('القالب غير نشط');
      }

      final db = await _databaseHelper.database;
      final now = DateTime.now();
      final effective = effectiveDate ?? now;

      // إنهاء المكونات الحالية للموظف
      await db.update(
        AppConstants.salaryDetailsTable,
        {
          'effective_to': effective.toIso8601String(),
          'updated_at': now.toIso8601String(),
        },
        where: 'employee_id = ? AND effective_to IS NULL',
        whereArgs: [employeeId],
      );

      // تطبيق مكونات القالب الجديدة
      for (final component in template.components) {
        await db.insert(AppConstants.salaryDetailsTable, {
          'employee_id': employeeId,
          'component_type': component.type,
          'component_name': component.name,
          'amount': component.amount,
          'is_percentage': component.isPercentage ? 1 : 0,
          'percentage_of': component.percentageOf,
          'effective_from': effective.toIso8601String(),
          'created_at': now.toIso8601String(),
          'updated_at': now.toIso8601String(),
        });
      }

      // تحديث الراتب الأساسي للموظف
      await db.update(
        AppConstants.employeesTable,
        {
          'basic_salary': template.basicSalary,
          'updated_at': now.toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [employeeId],
      );

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'APPLY_TEMPLATE',
        entityType: 'SalaryTemplate',
        entityId: templateId,
        description: 'تطبيق قالب راتب على موظف',
        newValues: {
          'template_id': templateId,
          'employee_id': employeeId,
          'effective_date': effective.toIso8601String(),
        },
      );

      LoggingService.info(
        'تم تطبيق قالب الراتب على الموظف بنجاح',
        category: 'SalaryTemplateService',
        data: {
          'template_id': templateId,
          'employee_id': employeeId,
          'template_name': template.name,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تطبيق قالب الراتب',
        category: 'SalaryTemplateService',
        data: {
          'template_id': templateId,
          'employee_id': employeeId,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// تعيين قالب كافتراضي
  Future<void> setAsDefault(int templateId) async {
    try {
      final template = await getTemplateById(templateId);
      if (template == null) {
        throw ValidationException('القالب غير موجود');
      }

      if (!template.isActive) {
        throw ValidationException('لا يمكن تعيين قالب غير نشط كافتراضي');
      }

      final db = await _databaseHelper.database;

      // إلغاء الافتراضية من جميع القوالب
      await _clearDefaultTemplates();

      // تعيين القالب الحالي كافتراضي
      await db.update(
        AppConstants.salaryTemplatesTable,
        {'is_default': 1, 'updated_at': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [templateId],
      );

      LoggingService.info(
        'تم تعيين القالب كافتراضي',
        category: 'SalaryTemplateService',
        data: {'template_id': templateId, 'name': template.name},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تعيين القالب كافتراضي',
        category: 'SalaryTemplateService',
        data: {'template_id': templateId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تفعيل/إلغاء تفعيل قالب
  Future<void> toggleTemplateStatus(int templateId) async {
    try {
      final template = await getTemplateById(templateId);
      if (template == null) {
        throw ValidationException('القالب غير موجود');
      }

      final db = await _databaseHelper.database;
      final newStatus = !template.isActive;

      // إذا كان القالب افتراضي ويتم إلغاء تفعيله، إلغاء الافتراضية
      Map<String, dynamic> updateData = {
        'is_active': newStatus ? 1 : 0,
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (!newStatus && template.isDefault) {
        updateData['is_default'] = 0;
      }

      await db.update(
        AppConstants.salaryTemplatesTable,
        updateData,
        where: 'id = ?',
        whereArgs: [templateId],
      );

      LoggingService.info(
        'تم تغيير حالة القالب',
        category: 'SalaryTemplateService',
        data: {
          'template_id': templateId,
          'name': template.name,
          'new_status': newStatus,
        },
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تغيير حالة القالب',
        category: 'SalaryTemplateService',
        data: {'template_id': templateId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على إحصائيات القوالب
  Future<Map<String, dynamic>> getTemplateStatistics() async {
    try {
      final db = await _databaseHelper.database;

      final totalResult = await db.rawQuery(
        'SELECT COUNT(*) as total FROM ${AppConstants.salaryTemplatesTable}',
      );

      final activeResult = await db.rawQuery(
        'SELECT COUNT(*) as active FROM ${AppConstants.salaryTemplatesTable} WHERE is_active = 1',
      );

      final defaultResult = await db.rawQuery(
        'SELECT COUNT(*) as default_count FROM ${AppConstants.salaryTemplatesTable} WHERE is_default = 1',
      );

      return {
        'total': totalResult.first['total'] as int,
        'active': activeResult.first['active'] as int,
        'inactive':
            (totalResult.first['total'] as int) -
            (activeResult.first['active'] as int),
        'has_default': (defaultResult.first['default_count'] as int) > 0,
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب إحصائيات القوالب',
        category: 'SalaryTemplateService',
        data: {'error': e.toString()},
      );
      return {'total': 0, 'active': 0, 'inactive': 0, 'has_default': false};
    }
  }

  /// دوال مساعدة خاصة

  /// التحقق من صحة بيانات القالب
  Future<void> _validateTemplate(SalaryTemplate template) async {
    if (template.name.trim().isEmpty) {
      throw ValidationException('اسم القالب مطلوب');
    }

    if (template.name.length < 3) {
      throw ValidationException('اسم القالب يجب أن يكون 3 أحرف على الأقل');
    }

    if (template.basicSalary < 0) {
      throw ValidationException('الراتب الأساسي لا يمكن أن يكون سالباً');
    }

    // التحقق من صحة المكونات
    for (final component in template.components) {
      if (component.name.trim().isEmpty) {
        throw ValidationException('اسم مكون الراتب مطلوب');
      }

      if (component.amount < 0) {
        throw ValidationException('مبلغ مكون الراتب لا يمكن أن يكون سالباً');
      }

      if (component.isPercentage && component.amount > 100) {
        throw ValidationException('النسبة المئوية لا يمكن أن تزيد عن 100%');
      }
    }
  }

  /// الحصول على قالب بالاسم
  Future<SalaryTemplate?> _getTemplateByName(String name) async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.query(
        AppConstants.salaryTemplatesTable,
        where: 'name = ?',
        whereArgs: [name.trim()],
        limit: 1,
      );

      if (result.isNotEmpty) {
        return SalaryTemplate.fromMap(result.first);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// إلغاء الافتراضية من جميع القوالب
  Future<void> _clearDefaultTemplates() async {
    final db = await _databaseHelper.database;
    await db.update(
      AppConstants.salaryTemplatesTable,
      {'is_default': 0, 'updated_at': DateTime.now().toIso8601String()},
      where: 'is_default = ?',
      whereArgs: [1],
    );
  }

  /// التحقق من استخدام القالب
  Future<bool> _isTemplateUsed(int templateId) async {
    try {
      final db = await _databaseHelper.database;

      // فحص استخدام القالب في تفاصيل الرواتب (يمكن إضافة جدول ربط لاحقاً)
      // حالياً نفترض عدم الاستخدام للسماح بالحذف
      return false;
    } catch (e) {
      return true; // في حالة الخطأ، نفترض أنه مستخدم لمنع الحذف
    }
  }
}
