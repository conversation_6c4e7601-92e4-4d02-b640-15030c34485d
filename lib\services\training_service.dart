/// خدمة التدريب وتطوير الموظفين
/// توفر جميع العمليات المتعلقة بإدارة التدريب والتطوير المهني
library;

import '../database/database_helper.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';
import '../exceptions/validation_exception.dart';
import '../models/hr_models.dart';
import '../constants/app_constants.dart';

class TrainingService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إنشاء جداول التدريب
  Future<void> _createTrainingTables() async {
    final db = await _databaseHelper.database;

    // جدول برامج التدريب
    await db.execute('''
      CREATE TABLE IF NOT EXISTS training_programs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        category TEXT NOT NULL,
        level TEXT NOT NULL,
        duration_hours INTEGER NOT NULL,
        cost REAL DEFAULT 0,
        provider TEXT,
        location TEXT,
        delivery_method TEXT DEFAULT 'offline',
        max_participants INTEGER DEFAULT 20,
        prerequisites TEXT,
        objectives TEXT,
        materials TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // جدول جلسات التدريب
    await db.execute('''
      CREATE TABLE IF NOT EXISTS training_sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        program_id INTEGER NOT NULL,
        title TEXT NOT NULL,
        start_date TEXT NOT NULL,
        end_date TEXT NOT NULL,
        instructor TEXT,
        location TEXT,
        max_participants INTEGER DEFAULT 20,
        status TEXT DEFAULT 'scheduled',
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (program_id) REFERENCES training_programs (id)
      )
    ''');

    // جدول تسجيل الموظفين في التدريب
    await db.execute('''
      CREATE TABLE IF NOT EXISTS training_enrollments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        session_id INTEGER NOT NULL,
        enrollment_date TEXT NOT NULL,
        status TEXT DEFAULT 'enrolled',
        score REAL,
        feedback TEXT,
        certificate TEXT,
        completion_date TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (employee_id) REFERENCES employees (id),
        FOREIGN KEY (session_id) REFERENCES training_sessions (id),
        UNIQUE(employee_id, session_id)
      )
    ''');

    // جدول مهارات الموظفين
    await db.execute('''
      CREATE TABLE IF NOT EXISTS employee_skills (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        skill_name TEXT NOT NULL,
        category TEXT NOT NULL,
        level INTEGER NOT NULL CHECK (level >= 1 AND level <= 5),
        description TEXT,
        acquired_date TEXT,
        last_assessed_date TEXT,
        certification_path TEXT,
        is_verified BOOLEAN DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (employee_id) REFERENCES employees (id),
        UNIQUE(employee_id, skill_name)
      )
    ''');

    // جدول خطط التطوير المهني
    await db.execute('''
      CREATE TABLE IF NOT EXISTS development_plans (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        start_date TEXT NOT NULL,
        target_date TEXT NOT NULL,
        status TEXT DEFAULT 'draft',
        goals TEXT,
        required_skills TEXT,
        suggested_training TEXT,
        mentor_id INTEGER,
        progress TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (employee_id) REFERENCES employees (id),
        FOREIGN KEY (mentor_id) REFERENCES employees (id)
      )
    ''');
  }

  /// إضافة برنامج تدريب جديد
  Future<TrainingProgram> addTrainingProgram(TrainingProgram program) async {
    try {
      await _createTrainingTables();

      // التحقق من عدم تكرار اسم البرنامج
      final existing = await _getProgramByName(program.name);
      if (existing != null) {
        throw ValidationException('يوجد برنامج تدريب بنفس الاسم');
      }

      final db = await _databaseHelper.database;
      final id = await db.insert('training_programs', program.toMap());

      final savedProgram = program.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'TrainingProgram',
        entityId: id,
        description: 'إضافة برنامج تدريب جديد: ${program.name}',
        newValues: savedProgram.toMap(),
      );

      LoggingService.info(
        'تم إضافة برنامج تدريب جديد',
        category: 'TrainingService',
        data: {'programId': id, 'name': program.name},
      );

      return savedProgram;
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة برنامج التدريب',
        category: 'TrainingService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على جميع برامج التدريب
  Future<List<TrainingProgram>> getAllTrainingPrograms({
    String? category,
    String? level,
    bool? isActive,
  }) async {
    try {
      await _createTrainingTables();
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (category != null) {
        whereClause += ' AND category = ?';
        whereArgs.add(category);
      }

      if (level != null) {
        whereClause += ' AND level = ?';
        whereArgs.add(level);
      }

      if (isActive != null) {
        whereClause += ' AND is_active = ?';
        whereArgs.add(isActive ? 1 : 0);
      }

      final result = await db.query(
        'training_programs',
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'name',
      );

      return result.map((map) => TrainingProgram.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب برامج التدريب',
        category: 'TrainingService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// إضافة جلسة تدريب
  Future<TrainingSession> addTrainingSession(TrainingSession session) async {
    try {
      await _createTrainingTables();

      // التحقق من وجود البرنامج
      final program = await _getProgramById(session.programId);
      if (program == null) {
        throw ValidationException('برنامج التدريب غير موجود');
      }

      // التحقق من صحة التواريخ
      if (session.startDate.isAfter(session.endDate)) {
        throw ValidationException(
          'تاريخ البداية لا يمكن أن يكون بعد تاريخ النهاية',
        );
      }

      final db = await _databaseHelper.database;
      final id = await db.insert('training_sessions', session.toMap());

      final savedSession = session.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'TrainingSession',
        entityId: id,
        description: 'إضافة جلسة تدريب جديدة: ${session.title}',
        newValues: savedSession.toMap(),
      );

      LoggingService.info(
        'تم إضافة جلسة تدريب جديدة',
        category: 'TrainingService',
        data: {'sessionId': id, 'title': session.title},
      );

      return savedSession;
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة جلسة التدريب',
        category: 'TrainingService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// تسجيل موظف في جلسة تدريب
  Future<TrainingEnrollment> enrollEmployee({
    required int employeeId,
    required int sessionId,
  }) async {
    try {
      await _createTrainingTables();

      // التحقق من وجود الجلسة
      final session = await _getSessionById(sessionId);
      if (session == null) {
        throw ValidationException('جلسة التدريب غير موجودة');
      }

      // التحقق من عدم التسجيل المسبق
      final existing = await _getEnrollment(employeeId, sessionId);
      if (existing != null) {
        throw ValidationException('الموظف مسجل مسبقاً في هذه الجلسة');
      }

      // التحقق من عدد المشاركين
      final currentEnrollments = await _getSessionEnrollments(sessionId);
      if (currentEnrollments.length >= session.maxParticipants) {
        throw ValidationException('تم الوصول للحد الأقصى من المشاركين');
      }

      final enrollment = TrainingEnrollment(
        employeeId: employeeId,
        sessionId: sessionId,
        enrollmentDate: DateTime.now(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final db = await _databaseHelper.database;
      final id = await db.insert('training_enrollments', enrollment.toMap());

      final savedEnrollment = enrollment.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'TrainingEnrollment',
        entityId: id,
        description: 'تسجيل موظف في جلسة تدريب',
        newValues: savedEnrollment.toMap(),
      );

      LoggingService.info(
        'تم تسجيل موظف في جلسة تدريب',
        category: 'TrainingService',
        data: {
          'enrollmentId': id,
          'employeeId': employeeId,
          'sessionId': sessionId,
        },
      );

      return savedEnrollment;
    } catch (e) {
      LoggingService.error(
        'خطأ في تسجيل الموظف في التدريب',
        category: 'TrainingService',
        data: {
          'employeeId': employeeId,
          'sessionId': sessionId,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// إضافة مهارة للموظف
  Future<EmployeeSkill> addEmployeeSkill(EmployeeSkill skill) async {
    try {
      await _createTrainingTables();

      // التحقق من عدم تكرار المهارة
      final existing = await _getEmployeeSkill(
        skill.employeeId,
        skill.skillName,
      );
      if (existing != null) {
        throw ValidationException('المهارة موجودة مسبقاً للموظف');
      }

      final db = await _databaseHelper.database;
      final id = await db.insert('employee_skills', skill.toMap());

      final savedSkill = skill.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'EmployeeSkill',
        entityId: id,
        description: 'إضافة مهارة للموظف: ${skill.skillName}',
        newValues: savedSkill.toMap(),
      );

      LoggingService.info(
        'تم إضافة مهارة للموظف',
        category: 'TrainingService',
        data: {
          'skillId': id,
          'employeeId': skill.employeeId,
          'skillName': skill.skillName,
        },
      );

      return savedSkill;
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة مهارة الموظف',
        category: 'TrainingService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  // دوال مساعدة خاصة
  Future<TrainingProgram?> _getProgramByName(String name) async {
    final db = await _databaseHelper.database;
    final result = await db.query(
      'training_programs',
      where: 'name = ?',
      whereArgs: [name],
      limit: 1,
    );
    return result.isNotEmpty ? TrainingProgram.fromMap(result.first) : null;
  }

  Future<TrainingProgram?> _getProgramById(int id) async {
    final db = await _databaseHelper.database;
    final result = await db.query(
      'training_programs',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );
    return result.isNotEmpty ? TrainingProgram.fromMap(result.first) : null;
  }

  Future<TrainingSession?> _getSessionById(int id) async {
    final db = await _databaseHelper.database;
    final result = await db.query(
      'training_sessions',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );
    return result.isNotEmpty ? TrainingSession.fromMap(result.first) : null;
  }

  Future<TrainingEnrollment?> _getEnrollment(
    int employeeId,
    int sessionId,
  ) async {
    final db = await _databaseHelper.database;
    final result = await db.query(
      'training_enrollments',
      where: 'employee_id = ? AND session_id = ?',
      whereArgs: [employeeId, sessionId],
      limit: 1,
    );
    return result.isNotEmpty ? TrainingEnrollment.fromMap(result.first) : null;
  }

  Future<List<TrainingEnrollment>> _getSessionEnrollments(int sessionId) async {
    final db = await _databaseHelper.database;
    final result = await db.query(
      'training_enrollments',
      where: 'session_id = ?',
      whereArgs: [sessionId],
    );
    return result.map((map) => TrainingEnrollment.fromMap(map)).toList();
  }

  Future<EmployeeSkill?> _getEmployeeSkill(
    int employeeId,
    String skillName,
  ) async {
    final db = await _databaseHelper.database;
    final result = await db.query(
      'employee_skills',
      where: 'employee_id = ? AND skill_name = ?',
      whereArgs: [employeeId, skillName],
      limit: 1,
    );
    return result.isNotEmpty ? EmployeeSkill.fromMap(result.first) : null;
  }

  /// الحصول على جلسات التدريب
  Future<List<TrainingSession>> getTrainingSessions({
    int? programId,
    String? status,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      await _createTrainingTables();
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (programId != null) {
        whereClause += ' AND program_id = ?';
        whereArgs.add(programId);
      }

      if (status != null) {
        whereClause += ' AND status = ?';
        whereArgs.add(status);
      }

      if (fromDate != null) {
        whereClause += ' AND start_date >= ?';
        whereArgs.add(fromDate.toIso8601String().split('T')[0]);
      }

      if (toDate != null) {
        whereClause += ' AND end_date <= ?';
        whereArgs.add(toDate.toIso8601String().split('T')[0]);
      }

      final result = await db.query(
        'training_sessions',
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'start_date',
      );

      return result.map((map) => TrainingSession.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب جلسات التدريب',
        category: 'TrainingService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على تسجيلات الموظف في التدريب
  Future<List<TrainingEnrollment>> getEmployeeEnrollments(
    int employeeId,
  ) async {
    try {
      await _createTrainingTables();
      final db = await _databaseHelper.database;

      final result = await db.query(
        'training_enrollments',
        where: 'employee_id = ?',
        whereArgs: [employeeId],
        orderBy: 'enrollment_date DESC',
      );

      return result.map((map) => TrainingEnrollment.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب تسجيلات التدريب للموظف',
        category: 'TrainingService',
        data: {'employeeId': employeeId, 'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على مهارات الموظف
  Future<List<EmployeeSkill>> getEmployeeSkills(int employeeId) async {
    try {
      await _createTrainingTables();
      final db = await _databaseHelper.database;

      final result = await db.query(
        'employee_skills',
        where: 'employee_id = ?',
        whereArgs: [employeeId],
        orderBy: 'category, skill_name',
      );

      return result.map((map) => EmployeeSkill.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب مهارات الموظف',
        category: 'TrainingService',
        data: {'employeeId': employeeId, 'error': e.toString()},
      );
      return [];
    }
  }

  /// إضافة خطة تطوير مهني
  Future<DevelopmentPlan> addDevelopmentPlan(DevelopmentPlan plan) async {
    try {
      await _createTrainingTables();

      // التحقق من صحة التواريخ
      if (plan.startDate.isAfter(plan.targetDate)) {
        throw ValidationException(
          'تاريخ البداية لا يمكن أن يكون بعد التاريخ المستهدف',
        );
      }

      final db = await _databaseHelper.database;
      final id = await db.insert('development_plans', plan.toMap());

      final savedPlan = plan.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'DevelopmentPlan',
        entityId: id,
        description: 'إضافة خطة تطوير مهني: ${plan.title}',
        newValues: savedPlan.toMap(),
      );

      LoggingService.info(
        'تم إضافة خطة تطوير مهني',
        category: 'TrainingService',
        data: {
          'planId': id,
          'employeeId': plan.employeeId,
          'title': plan.title,
        },
      );

      return savedPlan;
    } catch (e) {
      LoggingService.error(
        'خطأ في إضافة خطة التطوير المهني',
        category: 'TrainingService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على خطط التطوير المهني
  Future<List<DevelopmentPlan>> getDevelopmentPlans({
    int? employeeId,
    String? status,
  }) async {
    try {
      await _createTrainingTables();
      final db = await _databaseHelper.database;

      String whereClause = '1=1';
      List<dynamic> whereArgs = [];

      if (employeeId != null) {
        whereClause += ' AND employee_id = ?';
        whereArgs.add(employeeId);
      }

      if (status != null) {
        whereClause += ' AND status = ?';
        whereArgs.add(status);
      }

      final result = await db.query(
        'development_plans',
        where: whereClause,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'target_date',
      );

      return result.map((map) => DevelopmentPlan.fromMap(map)).toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب خطط التطوير المهني',
        category: 'TrainingService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// تحديث حالة التسجيل في التدريب
  Future<TrainingEnrollment> updateEnrollmentStatus({
    required int enrollmentId,
    required String status,
    double? score,
    String? feedback,
    String? certificate,
  }) async {
    try {
      await _createTrainingTables();
      final db = await _databaseHelper.database;

      final updateData = <String, dynamic>{
        'status': status,
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (score != null) updateData['score'] = score;
      if (feedback != null) updateData['feedback'] = feedback;
      if (certificate != null) updateData['certificate'] = certificate;

      if (status == 'completed') {
        updateData['completion_date'] = DateTime.now().toIso8601String().split(
          'T',
        )[0];
      }

      await db.update(
        'training_enrollments',
        updateData,
        where: 'id = ?',
        whereArgs: [enrollmentId],
      );

      // الحصول على التسجيل المحدث
      final result = await db.query(
        'training_enrollments',
        where: 'id = ?',
        whereArgs: [enrollmentId],
        limit: 1,
      );

      if (result.isEmpty) {
        throw ValidationException('تسجيل التدريب غير موجود');
      }

      final updatedEnrollment = TrainingEnrollment.fromMap(result.first);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'UPDATE',
        entityType: 'TrainingEnrollment',
        entityId: enrollmentId,
        description: 'تحديث حالة التسجيل في التدريب إلى: $status',
        newValues: updatedEnrollment.toMap(),
      );

      LoggingService.info(
        'تم تحديث حالة التسجيل في التدريب',
        category: 'TrainingService',
        data: {'enrollmentId': enrollmentId, 'status': status},
      );

      return updatedEnrollment;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث حالة التسجيل في التدريب',
        category: 'TrainingService',
        data: {'enrollmentId': enrollmentId, 'error': e.toString()},
      );
      rethrow;
    }
  }

  /// الحصول على إحصائيات التدريب
  Future<Map<String, dynamic>> getTrainingStatistics() async {
    try {
      await _createTrainingTables();
      final db = await _databaseHelper.database;

      // إحصائيات البرامج
      final programsCount = await db.rawQuery(
        'SELECT COUNT(*) as count FROM training_programs WHERE is_active = 1',
      );
      final sessionsCount = await db.rawQuery(
        'SELECT COUNT(*) as count FROM training_sessions',
      );
      final enrollmentsCount = await db.rawQuery(
        'SELECT COUNT(*) as count FROM training_enrollments',
      );
      final completedCount = await db.rawQuery(
        'SELECT COUNT(*) as count FROM training_enrollments WHERE status = "completed"',
      );

      // أكثر البرامج شعبية
      final popularPrograms = await db.rawQuery('''
        SELECT tp.name, COUNT(te.id) as enrollments
        FROM training_programs tp
        LEFT JOIN training_sessions ts ON tp.id = ts.program_id
        LEFT JOIN training_enrollments te ON ts.id = te.session_id
        WHERE tp.is_active = 1
        GROUP BY tp.id, tp.name
        ORDER BY enrollments DESC
        LIMIT 5
      ''');

      // إحصائيات المهارات
      final skillsCount = await db.rawQuery(
        'SELECT COUNT(*) as count FROM employee_skills',
      );
      final verifiedSkillsCount = await db.rawQuery(
        'SELECT COUNT(*) as count FROM employee_skills WHERE is_verified = 1',
      );

      return {
        'totalPrograms': programsCount.first['count'] ?? 0,
        'totalSessions': sessionsCount.first['count'] ?? 0,
        'totalEnrollments': enrollmentsCount.first['count'] ?? 0,
        'completedEnrollments': completedCount.first['count'] ?? 0,
        'totalSkills': skillsCount.first['count'] ?? 0,
        'verifiedSkills': verifiedSkillsCount.first['count'] ?? 0,
        'popularPrograms': popularPrograms,
        'completionRate': enrollmentsCount.first['count'] != 0
            ? ((completedCount.first['count'] as int) /
                      (enrollmentsCount.first['count'] as int) *
                      100)
                  .toStringAsFixed(1)
            : '0.0',
      };
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب إحصائيات التدريب',
        category: 'TrainingService',
        data: {'error': e.toString()},
      );
      return {};
    }
  }
}
